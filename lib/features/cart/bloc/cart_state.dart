import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:rozana/data/models/cart_model.dart';
import 'package:rozana/data/models/adress_model.dart';

part 'cart_state.freezed.dart';

enum OrderProcessingStatus {
  initial,
  processing,
  pendingPayment,
  verifyingPayment,
  success,
  error
}

enum StockAvailableStatus { inStock, outOfStock, quantityChecking }

@freezed
abstract class CartState with _$CartState {
  const factory CartState({
    required CartModel cart,
    required bool isLoading,
    String? appliedCoupon,
    String? error,
    AddressModel? deliveryAddress,
    @Default(false) bool isLoadingAddress,
    @Default(OrderProcessingStatus.initial) OrderProcessingStatus orderStatus,
    String? orderId,
    Map<String, dynamic>? orderData,
    @Default(StockAvailableStatus.quantityChecking)
    StockAvailableStatus stockStatus,
  }) = _CartState;

  factory CartState.initial() => CartState(
        cart: CartModel(),
        isLoading: false,
        appliedCoupon: null,
        error: null,
        deliveryAddress: null,
        isLoadingAddress: false,
        orderStatus: OrderProcessingStatus.initial,
        orderId: null,
        orderData: null,
        stockStatus: StockAvailableStatus.quantityChecking,
      );
}
