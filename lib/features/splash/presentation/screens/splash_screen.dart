import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../app/bloc/app_bloc.dart';
import '../../../../routes/app_router.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/services/user_profile_service.dart';
import '../../../profile/presentation/widgets/profile_setup_modal.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  Timer? _timeoutTimer;
  @override
  void initState() {
    super.initState();
    // Check if AppBloc is already loaded when splash screen initializes
    // This handles the case when app resumes from background with deeplink
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appState = context.read<AppBloc>().state;
      appState.maybeMap(
        loaded: (value) {
          _checkProfileAndNavigate(value.isAuthenticated);
        },
        orElse: () {
          _startTimeoutTimer();
        },
      );
    });
  }

  void _startTimeoutTimer() {
    _timeoutTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        context.go(RouteNames.home);
      }
    });
  }

  @override
  void dispose() {
    _timeoutTimer?.cancel();
    super.dispose();
  }

  Future<void> _checkProfileAndNavigate(bool isAuthenticated) async {
    if (isAuthenticated) {
      // Check if profile is complete
      final userProfileService = UserProfileService();
      final isProfileComplete = await userProfileService.isProfileComplete();

      if (!isProfileComplete && mounted) {
        // Show profile setup modal
        try {
          await showProfileSetupModal(context);
        } catch (e) {
          // Continue even if modal fails
        }
      }
    }

    // Navigate to home regardless of profile completion
    if (mounted) {
      context.go(RouteNames.home);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AppBloc, AppState>(
      listener: (context, state) {
        state.maybeMap(
          loaded: (value) {
            _checkProfileAndNavigate(value.isAuthenticated);
          },
          orElse: () {},
        );
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.7,
                height: MediaQuery.of(context).size.width * 0.5,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/images/Rozana_logo.webp'),
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              const SizedBox(height: 40),
              Text(
                'Groceries delivered in minutes',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.textGrey,
                      fontWeight: FontWeight.w500,
                    ),
              ),
              const SizedBox(height: 60),
              CircularProgressIndicator(
                color: AppColors.primary,
                strokeWidth: 3,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
