import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:rozana/domain/entities/product_entity.dart';

part 'product_detail_event.freezed.dart';

@freezed
abstract class ProductDetailEvent with _$ProductDetailEvent {
  const factory ProductDetailEvent.fetchProduct({
    required String sku,
    required String variantName,
  }) = _FetchProduct;

  const factory ProductDetailEvent.switchVariant({
    required ProductEntity variant,
  }) = _SwitchVariant;
}
