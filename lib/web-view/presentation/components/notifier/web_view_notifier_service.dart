import 'package:flutter/material.dart';

/// Service for managing web view notifications
class WebViewNotifierService {
  static final WebViewNotifierService _instance = WebViewNotifierService._internal();
  factory WebViewNotifierService() => _instance;
  WebViewNotifierService._internal();

  // Controller for managing notification state
  final ValueNotifier<WebViewNotification?> _notificationNotifier = ValueNotifier(null);
  ValueNotifier<WebViewNotification?> get notificationNotifier => _notificationNotifier;

  /// Show a notification in the web view
  void showNotification({
    required String message,
    NotificationType type = NotificationType.info,
    Duration duration = const Duration(seconds: 3),
  }) {
    _notificationNotifier.value = WebViewNotification(
      message: message,
      type: type,
      duration: duration,
    );
  }

  /// Hide the current notification
  void hideNotification() {
    _notificationNotifier.value = null;
  }
}

/// Types of notifications that can be shown
enum NotificationType {
  success,
  error,
  warning,
  info,
}

/// Model class for web view notifications
class WebViewNotification {
  final String message;
  final NotificationType type;
  final Duration duration;

  WebViewNotification({
    required this.message,
    required this.type,
    required this.duration,
  });
}
