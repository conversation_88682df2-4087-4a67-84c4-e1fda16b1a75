import 'package:rozana/data/models/cart_item_model.dart';

class CartModel {
  List<CartItemModel>? items;
  num? subTotal;
  num? tax;
  num? deliveryFee;
  num? discount;
  num? total;

  int get totalItems =>
      items?.fold(
          0, (sum, item) => (sum ?? 0) + (item.quantity ?? 0).toInt()) ??
      0;

  CartModel(
      {this.items,
      this.subTotal,
      this.tax,
      this.deliveryFee,
      this.discount,
      this.total});

  CartModel.fromJson(Map<String, dynamic> json) {
    if (json['items'] is List) {
      items = <CartItemModel>[];
      json['items'].forEach((v) {
        if (v is Map<String, dynamic>) {
          items!.add(CartItemModel.fromJson(v));
        }
      });
    }
    if (json['subtotal'] is num) {
      subTotal = json['subtotal'];
    } else if (json['subtotal'] != null) {
      var numb = num.tryParse(json['subtotal']!.toString());
      if (numb is num) {
        subTotal = numb;
      }
    }
    if (json['tax'] is num) {
      tax = json['tax'];
    } else if (json['tax'] != null) {
      var numb = num.tryParse(json['tax']!.toString());
      if (numb is num) {
        tax = numb;
      }
    }
    if (json['deliveryFee'] is num) {
      deliveryFee = json['deliveryFee'];
    } else if (json['deliveryFee'] != null) {
      var numb = num.tryParse(json['deliveryFee']!.toString());
      if (numb is num) {
        deliveryFee = numb;
      }
    }
    if (json['discount'] is num) {
      discount = json['discount'];
    } else if (json['discount'] != null) {
      var numb = num.tryParse(json['discount']!.toString());
      if (numb is num) {
        discount = numb;
      }
    }
    if (json['total'] is num) {
      total = json['total'];
    } else if (json['total'] != null) {
      var numb = num.tryParse(json['total']!.toString());
      if (numb is num) {
        total = numb;
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    // Add items to the JSON data
    if (items != null) {
      data['items'] = items!.map((item) => item.toJson()).toList();
    } else {
      data['items'] = [];
    }

    if (subTotal is num) {
      data['subtotal'] = subTotal;
    } else if (subTotal != null) {
      var numb = num.tryParse(subTotal.toString());
      if (numb is num) {
        data['subtotal'] = numb;
      }
    }
    if (tax is num) {
      data['tax'] = tax;
    } else if (tax != null) {
      var numb = num.tryParse(tax.toString());
      if (numb is num) {
        data['tax'] = numb;
      }
    }
    if (deliveryFee is num) {
      data['deliveryFee'] = deliveryFee;
    } else if (deliveryFee != null) {
      var numb = num.tryParse(deliveryFee.toString());
      if (numb is num) {
        data['deliveryFee'] = numb;
      }
    }
    if (discount is num) {
      data['discount'] = discount;
    } else if (discount != null) {
      var numb = num.tryParse(discount.toString());
      if (numb is num) {
        data['discount'] = numb;
      }
    }
    if (total is num) {
      data['total'] = total;
    } else if (total != null) {
      var numb = num.tryParse(total.toString());
      if (numb is num) {
        data['total'] = numb;
      }
    }

    return data;
  }

  CartModel copyWith({
    List<CartItemModel>? items,
    num? subTotal,
    num? tax,
    num? deliveryFee,
    num? discount,
    num? total,
  }) {
    return CartModel(
      items: items ?? this.items,
      subTotal: subTotal ?? this.subTotal,
      tax: tax ?? this.tax,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      discount: discount ?? this.discount,
      total: total ?? this.total,
    );
  }

  /// Calculate cart totals based on current items
  CartModel recalculate() {
    double newSubtotal = 0;
    num newTax = 0;

    // Calculate subtotal and tax based on each item's tax rate
    for (var item in (items ?? [])) {
      num itemSubtotal = (item.discountedPrice ?? 0) * (item.quantity ?? 0);
      newSubtotal += itemSubtotal;
      double taxRate = 0;
      if (item.taxable ?? false) {
        if (item.tax != null && item.tax! > 0) {
          taxRate = item.tax! / 100.0;
        } else if (item.cgst != null && item.sgst != null) {
          taxRate = (item.cgst! + item.sgst!) / 100.0;
        }
      }
      newTax += itemSubtotal * taxRate;
    }

    // Calculate delivery fee based on business rules:
    // - Free delivery for orders above ₹500
    // - Base delivery fee of ₹40
    // - Additional ₹20 fee for small orders under ₹200
    num newDeliveryFee = 0.0;
    if (newSubtotal <= 0) {
      newDeliveryFee = 0.0; // No delivery fee for empty cart
    } else if (newSubtotal < 200) {
      newDeliveryFee = 60.0; // ₹40 base + ₹20 small order fee
    } else if (newSubtotal < 500) {
      newDeliveryFee = 40.0; // Base delivery fee
    } // else free delivery (₹0)

    // Calculate total
    num newTotal = newSubtotal + newTax + newDeliveryFee - (discount ?? 0);

    return copyWith(
      subTotal: newSubtotal,
      tax: newTax,
      deliveryFee: newDeliveryFee,
      total: newTotal,
    );
  }

  /// Get a specific cart item by product ID
  CartItemModel? getItemByProductId(String productId) {
    try {
      return items?.firstWhere((item) => item.productId == productId);
    } catch (e) {
      return null;
    }
  }

  /// Check if the cart contains a specific product
  bool containsProduct(String productId) {
    return (items ?? []).any((item) => item.productId == productId);
  }
}
