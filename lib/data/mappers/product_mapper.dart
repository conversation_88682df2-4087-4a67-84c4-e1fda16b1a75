import '../../domain/entities/product_entity.dart';
import '../models/product_model.dart';

/// Mapper to convert between ProductModel and ProductEntity
class ProductMapper {
  /// Convert ProductModel to ProductEntity
  static ProductEntity toEntity(ProductModel model) {
    return ProductEntity(
      id: model.id ?? '',
      name: model.name ?? '',
      description: model.description ?? '',
      imageUrl: model.imageUrl,
      photos: model.photos,
      price: (model.price ?? 0).toDouble(),
      originalPrice: model.originalPrice?.toDouble(),
      rating: (model.rating ?? 0).toDouble(),
      reviewCount: (model.reviewCount ?? 0).toInt(),
      isInWishlist: model.isInWishlist ?? false,
      isOutOfStock: model.isOutOfStock ?? false,
      discountLabel: model.discountLabel,
      category: model.category ?? '',
      categoryId: model.categoryId,
      subcategory: model.subcategory,
      facilityId: model.facilityId,
      facilityName: model.facilityName,
      skuID: model.skuID,
      brandId: model.brandId,
      brandName: model.brandName,
      availableQty: model.availableQty?.toInt(),
      maxLimit: model.maxLimit?.toInt(),
      variantName: model.variantName,
      tax: model.tax,
      cgst: model.cgst,
      sgst: model.sgst,
      taxable: model.taxable,
      parentSku: model.parentSku,
      childSku: model.childSku,
      totalVariants: model.totalVariants,
      variants: model.variants?.map((variant) {
        return toEntity(variant);
      }).toList(),
    );
  }

  /// Convert ProductEntity to ProductModel
  static ProductModel toModel(ProductEntity entity) {
    return ProductModel(
      id: entity.id,
      name: entity.name,
      description: entity.description,
      imageUrl: entity.imageUrl,
      photos: entity.photos,
      price: entity.price,
      originalPrice: entity.originalPrice,
      rating: entity.rating,
      reviewCount: entity.reviewCount,
      isInWishlist: entity.isInWishlist,
      isOutOfStock: entity.isOutOfStock,
      discountLabel: entity.discountLabel,
      category: entity.category,
      categoryId: entity.categoryId,
      subcategory: entity.subcategory,
      facilityId: entity.facilityId,
      facilityName: entity.facilityName,
      skuID: entity.skuID,
      brandId: entity.brandId,
      brandName: entity.brandName,
      availableQty: entity.availableQty,
      maxLimit: entity.maxLimit,
      variantName: entity.variantName,
      parentSku: entity.parentSku,
      childSku: entity.childSku,
      totalVariants: entity.totalVariants,
      variants: entity.variants?.map((variant) => toModel(variant)).toList(),
      tax: entity.tax,
      cgst: entity.cgst,
      sgst: entity.sgst,
      taxable: entity.taxable,
    );
  }

  /// Convert list of ProductModel to list of ProductEntity
  static List<ProductEntity> toEntityList(List<ProductModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }

  /// Convert list of ProductEntity to list of ProductModel
  static List<ProductModel> toModelList(List<ProductEntity> entities) {
    return entities.map((entity) => toModel(entity)).toList();
  }

  static ProductEntity fromJson(Map<String, dynamic> json) {
    final model = ProductModel.fromJson(json);
    return toEntity(model);
  }

  static List<ProductEntity> fromJsonList(List<Map<String, dynamic>> jsonList) {
    return jsonList.map((json) => fromJson(json)).toList();
  }
}
