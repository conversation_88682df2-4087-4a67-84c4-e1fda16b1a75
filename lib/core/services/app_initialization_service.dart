import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import '../config/environment_config.dart';
import 'remote_config_service.dart';
import 'crash_analytics_service.dart';
import '../utils/logger.dart';

/// Service responsible for initializing app dependencies in the correct order
class AppInitializationService {
  static final AppInitializationService _instance =
      AppInitializationService._internal();

  factory AppInitializationService() => _instance;

  AppInitializationService._internal();

  /// Flag to track if initialization has been completed
  bool _isInitialized = false;

  /// Check if the app has been initialized
  bool get isInitialized => _isInitialized;

  /// Initialize all required services in the correct order
  /// This should be called before the app starts
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('AppInitializationService: Already initialized, skipping');
      return;
    }

    try {
      debugPrint('AppInitializationService: Starting initialization');

      // Initialize Firebase, Remote Config, and Crash Analytics in parallel for faster startup
      await Future.wait([
        _initializeFirebase(),
      ], eagerError: false)
          .timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          // If initialization takes too long, continue anyway
          LogMessage.p(
              'Some initialization timed out, continuing with app startup',
              color: Colors.orange);
          return [];
        },
      );

      await Future.wait([
        _initializeRemoteConfig(),
        _initializeCrashAnalytics(),
      ], eagerError: false)
          .timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          // If initialization takes too long, continue anyway
          LogMessage.p(
              'Remote Config/Crash Analytics initialization timed out, continuing with app startup',
              color: Colors.orange);
          return [];
        },
      );
      // Mark initialization as complete
      _isInitialized = true;
      debugPrint(
          'AppInitializationService: Initialization completed successfully');
    } catch (e) {
      debugPrint('AppInitializationService: Error during initialization: $e');
      // Continue with app startup even if there's an error
      _isInitialized = true;
    }
  }

  /// Initialize Firebase
  Future<void> _initializeFirebase() async {
    try {
      debugPrint('AppInitializationService: Initializing Firebase');

      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp(
          options: FirebaseOptions(
              apiKey: EnvironmentConfig.firebaseApiKey,
              authDomain: EnvironmentConfig.firebaseAuthDomain,
              projectId: EnvironmentConfig.firebaseProjectId,
              storageBucket: EnvironmentConfig.firebaseStorageBucket,
              messagingSenderId: EnvironmentConfig.firebaseMessagingSenderId,
              appId: EnvironmentConfig.firebaseAppId,
              measurementId: EnvironmentConfig.firebaseMeasurementId),
        );
        LogMessage.p('Firebase initialized successfully', color: Colors.green);
      } else {
        LogMessage.p('Firebase already initialized, skipping initialization',
            color: Colors.orange);
      }
    } catch (e) {
      LogMessage.p('Firebase initialization failed: $e', color: Colors.red);
    }
  }

  /// Initialize Remote Config
  Future<void> _initializeRemoteConfig() async {
    try {
      debugPrint('AppInitializationService: Initializing Remote Config');
      final remoteConfigService = RemoteConfigService();
      await remoteConfigService.initialize();
      debugPrint(
          'AppInitializationService: Remote Config initialized successfully');
    } catch (e) {
      debugPrint(
          'AppInitializationService: Error initializing Remote Config: $e');
      // In development mode, we can continue with default values
      if (EnvironmentConfig.environment != Environment.development) {
        rethrow;
      }
    }
  }

  /// Initialize Crash Analytics
  Future<void> _initializeCrashAnalytics() async {
    try {
      debugPrint('AppInitializationService: Initializing Crash Analytics');
      final crashAnalyticsService = CrashAnalyticsService();
      await crashAnalyticsService.initialize();
      LogMessage.p('Crash Analytics initialized successfully',
          color: Colors.green);
    } catch (e) {
      LogMessage.p('Crash Analytics initialization failed: $e',
          color: Colors.red);
      // Don't rethrow as the app can still function without crash analytics
    }
  }
}
