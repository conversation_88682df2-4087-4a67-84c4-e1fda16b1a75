export 'app_event.dart';
export 'app_state.dart';

import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/services/bloc_cleanup_service.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/core/services/user_profile_service.dart';
import 'package:rozana/features/auth/bloc/login_bloc/login_bloc.dart';
import 'package:sms_autofill/sms_autofill.dart';

import '../../core/services/appflyer_services/app_flyers_services.dart';
import '../../core/services/appflyer_services/appflyer_events.dart';
import 'app_event.dart';
import 'app_state.dart';

class AppBloc extends Bloc<AppEvent, AppState> {
  bool _isAuthenticated = false;
  bool get isAuthenticated => _isAuthenticated;

  // Store the return route for after login
  String? _returnRoute;

  AppBloc() : super(const AppState.initial()) {
    on<AppEvent>((event, emit) async {
      await event.map(
        appStarted: (_) => _onAppStarted(emit),
        loginRequested: (e) => _onLoginRequested(e.token, e.user, emit),
        loginWithProfileCheck: (e) =>
            _onLoginWithProfileCheck(e.token, e.user, emit),
        logoutRequested: (_) => _onLogoutRequested(emit),
      );
    });
  }

  Future<void> _onAppStarted(Emitter<AppState> emit) async {
    emit(const AppState.loading());

    try {
      final bool isAuthenticated = await checkAuthStatus();
      emit(AppState.loaded(isAuthenticated: isAuthenticated));
    } catch (e) {
      emit(const AppState.loaded(isAuthenticated: false));
    }
  }

  Future<void> _onLogoutRequested(Emitter<AppState> emit) async {
    try {
      await logout();
      final newState = state.maybeMap(
        loaded: (loaded) => loaded.copyWith(isAuthenticated: false),
        orElse: () => const AppState.loaded(isAuthenticated: false),
      );
      emit(newState);
    } catch (e) {
      LogMessage.p('Failed to logout user: $e');
    }
  }

  Future<void> _onLoginRequested(
      String token, Map<String, dynamic>? user, Emitter<AppState> emit) async {
    try {
      await _authenticateUser(token, user);
      final newState = state.maybeMap(
        loaded: (loaded) => loaded.copyWith(isAuthenticated: true),
        orElse: () => const AppState.loaded(isAuthenticated: true),
      );
      emit(newState);
    } catch (e) {
      LogMessage.p('Failed to login user: $e');
    }
  }

  Future<void> _onLoginWithProfileCheck(
      String token, Map<String, dynamic>? user, Emitter<AppState> emit) async {
    try {
      await _authenticateUser(token, user);

      // Check if user profile is complete for ALL users (new and existing)
      final userProfileService = UserProfileService();
      final isProfileComplete = await userProfileService.isProfileComplete();

      LogMessage.p('Profile completion check: $isProfileComplete');

      if (isProfileComplete) {
        // Profile is complete, proceed to home
        final newState = state.maybeMap(
          loaded: (loaded) => loaded.copyWith(isAuthenticated: true),
          orElse: () => const AppState.loaded(isAuthenticated: true),
        );
        emit(newState);
        LogMessage.p('User authenticated with complete profile → Home');
      } else {
        // Profile is incomplete, proceed to authenticated state
        // The UI will handle showing the profile setup modal
        final newState = state.maybeMap(
          loaded: (loaded) => loaded.copyWith(isAuthenticated: true),
          orElse: () => const AppState.loaded(isAuthenticated: true),
        );
        emit(newState);
        LogMessage.p(
            'User authenticated with incomplete profile → Will show modal');
      }
    } catch (e) {
      LogMessage.p('Failed to login user: $e');
    }
  }

  Future<void> _authenticateUser(
    String token,
    Map<String, dynamic>? user,
  ) async {
    _isAuthenticated = true;

    await AppPreferences.setToken(token);
    await AppPreferences.setLoginStatus(true);
    if (user != null) {
      await AppPreferences.setUserdata(jsonEncode(user));
      await AppsFlyerServices.pushUserProfile(user['uid'] ?? '');
      await AppsFlyerEvents.login(user['uid']);
    }
  }

  // Set the return route to navigate to after successful login
  void setReturnRoute(String route) {
    _returnRoute = route;
  }

  // Get and clear the return route
  String? getAndClearReturnRoute() {
    final route = _returnRoute;
    _returnRoute = null;
    return route;
  }

  Future<void> logout() async {
    _isAuthenticated = false;
    // Sign out from Firebase Auth
    try {
      await FirebaseAuth.instance.signOut();
      // Unregister SMS listener to prevent stale OTPs
      try {
        await SmsAutoFill().unregisterListener();
        LoginBloc.cleanupSmsResources();
      } catch (e) {
        LogMessage.p('Error unregistering SMS listener: $e');
      }
      // Clear SharedPreferences (including profile data)
      await AppPreferences.setLoginStatus(false);
      await AppPreferences.setToken('');
      await AppPreferences.setUserdata(''); // This clears profile data ✅
      // Clear cart data from SharedPreferences
      await AppPreferences.clearOne(AppPreferences.cartData);
      // Clear selected address ID
      await AppPreferences.clearSelectedAddressId();
      LogMessage.p('Successfully signed out from Firebase');
    } catch (e) {
      LogMessage.p('Error signing out from Firebase: $e');
    }
    // Reset user-specific BLoCs to prevent data leakage
    BlocCleanupService.resetUserSpecificBlocs();
  }

  Future<bool> checkAuthStatus() async {
    _isAuthenticated = AppPreferences.getLoginStatus() ?? false;
    return _isAuthenticated;
  }

  /// Reset user-specific data across all BLoCs
  void resetUserData() {
    // Reset all user-specific state in singleton BLoCs
    BlocCleanupService.resetUserSpecificBlocs();
  }

  @override
  Future<void> close() {
    // Clean up any app-level resources
    return super.close();
  }
}
